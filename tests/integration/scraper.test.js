import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import fs from 'fs/promises';
import path, { dirname } from 'path';
import { fileURLToPath } from 'url';

import ScraperService from '../../src/services/scraper.js';
import AuthService from '../../src/services/auth.js';
import config from '../../src/utils/config.js';
import logger from '../../src/utils/logger.js';

const __dirname = dirname(fileURLToPath(import.meta.url));
const fixturesPath = path.join(__dirname, '../fixtures/html');

vi.mock('playwright', () => {
  const mockPageSingleton = {
    goto: vi.fn(),
    locator: vi.fn(),
    setContent: vi.fn(),
    url: vi.fn().mockReturnValue(''),
    title: vi.fn().mockResolvedValue('Test Page Title'),
    content: vi.fn().mockResolvedValue('<html><body>Test content</body></html>'),
    evaluate: vi.fn(),
    context: vi.fn().mockReturnValue({
      cookies: vi.fn().mockResolvedValue([]),
    }),
    setViewportSize: vi.fn().mockResolvedValue(undefined),
    waitForLoadState: vi.fn().mockResolvedValue(undefined),
    waitForSelector: vi.fn().mockResolvedValue(undefined),
    close: vi.fn().mockResolvedValue(undefined),
  };

  const defaultMockLocatorActions = {
    all: vi.fn().mockResolvedValue([]),
    count: vi.fn().mockResolvedValue(0),
    getAttribute: vi.fn().mockResolvedValue(null),
    textContent: vi.fn().mockResolvedValue(''),
    fill: vi.fn().mockResolvedValue(undefined),
    click: vi.fn().mockResolvedValue(undefined),
    isVisible: vi.fn().mockResolvedValue(true),
    innerHTML: vi.fn().mockResolvedValue(''),
    allTextContents: vi.fn().mockResolvedValue([]),
    locator: vi.fn(() => defaultMockLocatorActions),
    first: vi.fn(() => defaultMockLocatorActions),
    evaluate: vi.fn(),
  };
  mockPageSingleton.locator.mockImplementation(() => ({ ...defaultMockLocatorActions }));

  const mockContext = {
    newPage: vi.fn().mockResolvedValue(mockPageSingleton),
    cookies: vi.fn().mockResolvedValue([]),
    close: vi.fn().mockResolvedValue(undefined),
  };
  const mockBrowser = {
    newContext: vi.fn().mockResolvedValue(mockContext),
    close: vi.fn().mockResolvedValue(undefined),
    isConnected: vi.fn().mockReturnValue(true),
  };
  return {
    chromium: {
      launch: vi.fn().mockResolvedValue(mockBrowser),
    },
  };
});

describe('ScraperService Integration Test with HTML Fixtures', () => {
  let authService;
  let scraperService;
  let page;

  beforeEach(async () => {
    const playwright = await import('playwright');
    page = await (await (await playwright.chromium.launch()).newContext()).newPage();

    Object.values(page).forEach(mockFn => {
      if (vi.isMockFunction(mockFn)) mockFn.mockClear();
    });
    
    const defaultLocatorMock = (includeWaitFor = false) => {
      const mock = {
          all: vi.fn().mockResolvedValue([]),
          count: vi.fn().mockResolvedValue(0),
          getAttribute: vi.fn().mockResolvedValue(null),
          textContent: vi.fn().mockResolvedValue(''),
          isVisible: vi.fn().mockResolvedValue(true),
          evaluate: vi.fn(),
          click: vi.fn(),
          fill: vi.fn(),
          innerHTML: vi.fn().mockResolvedValue(''),
          allTextContents: vi.fn().mockResolvedValue([]),
          locator: vi.fn(() => defaultLocatorMock(includeWaitFor)), // Chainable, pass includeWaitFor
          first: vi.fn(() => defaultLocatorMock(true)), // first() should return a locator that has waitFor
      };
      if (includeWaitFor) {
        mock.waitFor = vi.fn().mockResolvedValue(undefined);
      }
      return mock;
    };
    page.locator.mockImplementation(() => defaultLocatorMock());
    page.url.mockReturnValue('');

    page.goto.mockImplementation(async (url) => {
      let fixtureFile = '';
      if (url.includes('/kozelet')) fixtureFile = 'category_kozelet.html';
      else if (url.includes('/cikkek/123/')) fixtureFile = 'article_123.html';
      else {
        // Handle custom test URLs by returning basic HTML content
        page.url.mockReturnValue(url);
        return { status: () => 200 };
      }
      const htmlContent = await fs.readFile(path.join(fixturesPath, fixtureFile), 'utf-8');
      page.url.mockReturnValue(url);
      return { status: () => 200 };
    });

    // This defaultLocatorMock is defined within beforeEach, so it's fresh each time.
    // The page.locator is initially set to return this default, then overridden by the more specific logic below.
    
    page.locator.mockImplementation(selector => {
      const kozeletContainerSelector = config.mti.categoryContainerSelectors.kozelet;
      const articleItemSelector = 'a.group.mb-5.block'; // Hardcoded in scraper.js

      // Mock for the 'kozelet' category container
      if (selector === kozeletContainerSelector) {
        const mockContainer = defaultLocatorMock(); 
        mockContainer.count.mockResolvedValue(1);   // Container is found

        // Mock the .locator() call on this container to find article items
        mockContainer.locator.mockImplementation(innerSelector => {
          if (innerSelector === articleItemSelector) {
            const articleListLocator = defaultLocatorMock();
            
            // Define the mock for a single article link element
            const mockArticleLinkElement = defaultLocatorMock();
            mockArticleLinkElement.getAttribute.mockResolvedValue('/cikkek/123/teszt-cikk-a-kozeletbol');
            // Mock locators for details within the article link (img, h2, summary)
            mockArticleLinkElement.locator.mockImplementation((articleDetailSelector) => {
              const detailLocator = defaultLocatorMock();
              if (articleDetailSelector === 'img') {
                detailLocator.getAttribute.mockResolvedValue('https://mti.hu/test-image.jpg');
                detailLocator.count.mockResolvedValue(1);
              } else if (articleDetailSelector === 'h2') {
                detailLocator.textContent.mockResolvedValue('Teszt Cikk a Közéletből');
                detailLocator.count.mockResolvedValue(1);
              } else if (articleDetailSelector === 'h2 + div div') {
                detailLocator.textContent.mockResolvedValue('Ez egy teszt összefoglaló a kategória oldalon.');
                detailLocator.count.mockResolvedValue(1);
              }
              return detailLocator;
            });

            articleListLocator.all.mockResolvedValue([mockArticleLinkElement]); // Returns a list containing one mock article
            return articleListLocator;
          }
          return defaultLocatorMock(); // Default for other selectors on the container
        });
        return mockContainer;
      }

      // Mock for the date element on the article page
      if (selector === 'span.py-0\\.75:nth-child(2)') {
        const dateLocator = defaultLocatorMock();
        dateLocator.count.mockResolvedValue(1);
        dateLocator.textContent.mockResolvedValue('2024. január 15., hétfő 10:30'); 
        return dateLocator;
      }

      // Mock for the content element on the article page
      if (selector === '.svelte-1tcypa1') {
        // The contentLocator needs the .locator('p').first().waitFor() to be mocked
        const contentLocator = defaultLocatorMock(); // Base mock
        contentLocator.count.mockResolvedValue(1);

        // Mock the .locator('p') call on this contentLocator
        const paragraphLocator = defaultLocatorMock(true); // This needs waitFor functionality
        contentLocator.locator.mockImplementation(innerSelector => {
          if (innerSelector === 'p') {
            return paragraphLocator; // Return the paragraphLocator which has .first().waitFor()
          }
          return defaultLocatorMock();
        });
        
        // The paragraphLocator.first() already returns a waitFor-capable object due to defaultLocatorMock(true).
        // The paragraphLocator.first().waitFor() is already mocked by defaultLocatorMock(true).

        // Mock innerHTML() to return the expected content
        contentLocator.innerHTML.mockResolvedValue('<p>Ez egy teszt összefoglaló a kategória oldalon.</p><p>Ez a cikk <strong>tényleges</strong> tartalma, ami hosszabb.</p><p>Még egy bekezdés a <span>tartalomhoz</span>.</p>');

        contentLocator.evaluate.mockImplementation((evalFunc, summary) => {
          let html = `<p>${summary}</p><p>Ez a cikk <strong>tényleges</strong> tartalma, ami hosszabb.</p><p>Még egy bekezdés a <span>tartalomhoz</span>.</p>`;
          if (summary && html.startsWith(`<p>${summary}</p>`)) {
            html = html.substring(`<p>${summary}</p>`.length);
          }
          return html.replace(/class="[^"]*"/g, '').replace(/ class/g, '');
        });
        return contentLocator;
      }
      
      // Fallback for any other selector passed directly to page.locator()
      return defaultLocatorMock();
    });
    
    authService = new AuthService();
    await authService.initBrowser(); 
    authService.isAuthenticated = true; 

    scraperService = new ScraperService(authService, config, logger);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('should scrape one article from a specific category', async () => {
    const targetCategory = 'kozelet';
    const maxArticles = 1;
    const existingUrls = new Set(); // Pass an empty set for existing URLs

    const articles = await scraperService.scrapeCategories(existingUrls, targetCategory, maxArticles);

    expect(articles).toBeInstanceOf(Array);
    expect(articles).toHaveLength(1);

    const article = articles[0];
    expect(article).toHaveProperty('url', `${config.mti.baseUrl}/cikkek/123/teszt-cikk-a-kozeletbol`); // Changed 'articleUrl' to 'url'
    expect(article).toHaveProperty('title', 'Teszt Cikk a Közéletből');
    expect(article).toHaveProperty('summary', 'Ez egy teszt összefoglaló a kategória oldalon.');
    expect(article).toHaveProperty('tags');
    expect(article.tags).toBeInstanceOf(Array);
    expect(article.tags).toContain(targetCategory);
    expect(article).toHaveProperty('image', 'https://mti.hu/test-image.jpg'); // Changed 'imageUrl' to 'image'
    
    expect(article).toHaveProperty('content_html'); // Changed 'contentHtml' to 'content_html'
    expect(article.content_html).toContain('Ez a cikk <strong>tényleges</strong> tartalma'); // Changed 'contentHtml' to 'content_html'

    expect(article).toHaveProperty('date_published'); // Changed 'datePublished' to 'date_published'
    expect(new Date(article.date_published).toISOString()).toEqual("2024-01-15T10:30:00.000Z"); // Changed 'datePublished' to 'date_published'
  });

  describe('Error Handling Tests', () => {
    
    describe('Authentication Errors', () => {
      it('should return empty array when authentication fails before category scraping', async () => {
        authService.isAuthenticated = false;
        
        const articles = await scraperService.scrapeCategories(new Set(), 'kozelet', 1);
        
        expect(articles).toEqual([]);
      });

      it('should throw AuthenticationError when ensureAuthenticated fails before category page', async () => {
        // Mock ensureAuthenticated to return false
        vi.spyOn(scraperService, 'ensureAuthenticated').mockResolvedValue(false);
        
        await expect(scraperService.scrapeCategoryPage(page, 'kozelet'))
          .rejects.toThrow('Failed to scrape category page: https://mti.hu/kozelet');
      });

      it('should throw AuthenticationError when session expires after navigation', async () => {
        // Mock validateSession to return false (session expired)
        vi.spyOn(scraperService, 'validateSession').mockResolvedValue(false);
        // Mock ensureAuthenticated to return false (re-auth failed)
        vi.spyOn(scraperService, 'ensureAuthenticated').mockResolvedValue(false);
        
        await expect(scraperService.scrapeCategoryPage(page, 'kozelet'))
          .rejects.toThrow('Failed to scrape category page: https://mti.hu/kozelet');
      });

      it('should throw AuthenticationError when page not available from authService', async () => {
        // Mock getPage to return null
        authService.getPage = vi.fn().mockReturnValue(null);
        
        const articles = await scraperService.scrapeCategories(new Set(), 'kozelet', 1);
        
        expect(articles).toEqual([]);
      });
    });

    describe('Network and Timeout Errors', () => {
      it('should throw NetworkError on timeout when visiting category page', async () => {
        const timeoutError = new Error('Timeout');
        timeoutError.name = 'TimeoutError';
        page.goto.mockRejectedValue(timeoutError);
        
        await expect(scraperService.scrapeCategoryPage(page, 'kozelet'))
          .rejects.toThrow('Timeout visiting category page');
      });

      it('should handle network errors during article content scraping', async () => {
        const networkError = new Error('Network failure');
        page.goto.mockRejectedValue(networkError);
        
        const result = await scraperService.scrapeArticleContent(page, {
          url: 'https://mti.hu/test-url',
          summary: 'Test summary'
        });
        
        expect(result).toEqual({ content_html: null, date_published: null });
      });

      it('should handle timeout when visiting article page', async () => {
        const timeoutError = new Error('Timeout');
        timeoutError.name = 'TimeoutError';
        page.goto.mockRejectedValue(timeoutError);
        
        await expect(scraperService.scrapeArticleContent(page, {
          url: 'https://mti.hu/test-url',
          summary: 'Test summary'
        })).rejects.toThrow('Timeout visiting article page: https://mti.hu/test-url');
      });
    });

    describe('Scraping Structure Errors', () => {
      it('should return empty array when category container not found', async () => {
        // Mock container not found
        page.locator.mockImplementation(selector => {
          if (selector === config.mti.categoryContainerSelectors.kozelet) {
            const mockContainer = {
              count: vi.fn().mockResolvedValue(0) // Container not found
            };
            return mockContainer;
          }
          return { count: vi.fn().mockResolvedValue(0) };
        });
        
        const articles = await scraperService.scrapeCategoryPage(page, 'kozelet');
        
        expect(articles).toEqual([]);
      });

      it('should return null content when article content container not found', async () => {
        page.locator.mockImplementation(selector => {
          if (selector === '.svelte-1tcypa1') {
            return { count: vi.fn().mockResolvedValue(0) }; // Content container not found
          }
          if (selector === 'span.py-0\\.75:nth-child(2)') {
            const dateLocator = {
              count: vi.fn().mockResolvedValue(1),
              textContent: vi.fn().mockResolvedValue('2024. január 15., hétfő 10:30')
            };
            return dateLocator;
          }
          return { count: vi.fn().mockResolvedValue(0) };
        });
        
        const result = await scraperService.scrapeArticleContent(page, {
          url: 'https://mti.hu/test-url',
          summary: 'Test summary'
        });
        
        expect(result).toEqual({
          content_html: null,
          date_published: null
        });
      });

      it('should warn when date element not found', async () => {
        page.locator.mockImplementation(selector => {
          if (selector === 'span.py-0\\.75:nth-child(2)') {
            return { count: vi.fn().mockResolvedValue(0) }; // Date element not found
          }
          if (selector === '.svelte-1tcypa1') {
            const contentLocator = {
              count: vi.fn().mockResolvedValue(1),
              locator: vi.fn().mockReturnValue({
                first: vi.fn().mockReturnValue({
                  waitFor: vi.fn().mockResolvedValue(undefined)
                })
              }),
              evaluate: vi.fn().mockResolvedValue('<p>Test content</p>')
            };
            return contentLocator;
          }
          return { count: vi.fn().mockResolvedValue(0) };
        });
        
        const result = await scraperService.scrapeArticleContent(page, {
          url: 'https://mti.hu/test-url',
          summary: 'Test summary'
        });
        
        expect(result).toEqual({
          content_html: null,
          date_published: null
        });
      });

      it('should handle timeout waiting for content paragraphs', async () => {
        page.locator.mockImplementation(selector => {
          if (selector === '.svelte-1tcypa1') {
            const contentLocator = {
              count: vi.fn().mockResolvedValue(1),
              locator: vi.fn().mockReturnValue({
                first: vi.fn().mockReturnValue({
                  waitFor: vi.fn().mockRejectedValue(new Error('Timeout waiting for paragraphs'))
                }),
                count: vi.fn().mockResolvedValue(0)
              }),
              evaluate: vi.fn().mockResolvedValue('<p>Test content extracted despite timeout</p>')
            };
            return contentLocator;
          }
          return { count: vi.fn().mockResolvedValue(0) };
        });
        
        const result = await scraperService.scrapeArticleContent(page, {
          url: 'https://mti.hu/test-url',
          summary: 'Test summary'
        });
        
        expect(result).toEqual({
          content_html: null,
          date_published: null
        });
      });
    });

    describe('Data Validation Errors', () => {
      it('should skip articles with missing URL or title', async () => {
        page.locator.mockImplementation(selector => {
          const kozeletContainerSelector = config.mti.categoryContainerSelectors.kozelet;
          
          if (selector === kozeletContainerSelector) {
            const mockContainer = {
              count: vi.fn().mockResolvedValue(1),
              locator: vi.fn().mockImplementation(innerSelector => {
                if (innerSelector === 'a.group.mb-5.block') {
                  const articleListLocator = {
                    all: vi.fn().mockResolvedValue([
                      {
                        getAttribute: vi.fn().mockResolvedValue(null), // Missing href
                        locator: vi.fn().mockReturnValue({
                          count: vi.fn().mockResolvedValue(0),
                          textContent: vi.fn().mockResolvedValue(null),
                          getAttribute: vi.fn().mockResolvedValue(null)
                        }),
                        innerHTML: vi.fn().mockResolvedValue('<div>Invalid item</div>')
                      },
                      {
                        getAttribute: vi.fn().mockResolvedValue('/valid-url'),
                        locator: vi.fn().mockReturnValue({
                          count: vi.fn().mockResolvedValue(0),
                          textContent: vi.fn().mockResolvedValue(null), // Missing title
                          getAttribute: vi.fn().mockResolvedValue(null)
                        }),
                        innerHTML: vi.fn().mockResolvedValue('<div>No title item</div>')
                      }
                    ])
                  };
                  return articleListLocator;
                }
                return { count: vi.fn().mockResolvedValue(0) };
              })
            };
            return mockContainer;
          }
          return { count: vi.fn().mockResolvedValue(0) };
        });
        
        const articles = await scraperService.scrapeCategoryPage(page, 'kozelet');
        
        expect(articles).toEqual([]);
      });

      it('should handle date parsing failures gracefully', async () => {
        page.locator.mockImplementation(selector => {
          if (selector === 'span.py-0\\.75:nth-child(2)') {
            return {
              count: vi.fn().mockResolvedValue(1),
              textContent: vi.fn().mockResolvedValue('Invalid date format XYZ')
            };
          }
          if (selector === '.svelte-1tcypa1') {
            const contentLocator = {
              count: vi.fn().mockResolvedValue(1),
              locator: vi.fn().mockReturnValue({
                first: vi.fn().mockReturnValue({
                  waitFor: vi.fn().mockResolvedValue(undefined)
                })
              }),
              innerHTML: vi.fn().mockResolvedValue('<p>Test content</p>'),
              evaluate: vi.fn().mockResolvedValue('<p>Test content</p>')
            };
            return contentLocator;
          }
          return { count: vi.fn().mockResolvedValue(0) };
        });
        
        const result = await scraperService.scrapeArticleContent(page, {
          url: 'https://mti.hu/test-url',
          summary: 'Test summary'
        });
        
        expect(result).toEqual({
          content_html: '<p>Test content</p>',
          date_published: null
        });
      });

      it('should handle errors during individual article extraction', async () => {
        page.locator.mockImplementation(selector => {
          const kozeletContainerSelector = config.mti.categoryContainerSelectors.kozelet;
          
          if (selector === kozeletContainerSelector) {
            const mockContainer = {
              count: vi.fn().mockResolvedValue(1),
              locator: vi.fn().mockImplementation(innerSelector => {
                if (innerSelector === 'a.group.mb-5.block') {
                  const articleListLocator = {
                    all: vi.fn().mockResolvedValue([
                      {
                        getAttribute: vi.fn().mockRejectedValue(new Error('DOM error')),
                        locator: vi.fn().mockReturnValue({
                          count: vi.fn().mockResolvedValue(0)
                        }),
                        innerHTML: vi.fn().mockResolvedValue('<div>Error item</div>')
                      }
                    ])
                  };
                  return articleListLocator;
                }
                return { count: vi.fn().mockResolvedValue(0) };
              })
            };
            return mockContainer;
          }
          return { count: vi.fn().mockResolvedValue(0) };
        });
        
        const articles = await scraperService.scrapeCategoryPage(page, 'kozelet');
        
        expect(articles).toEqual([]);
      });
    });

    describe('Edge Cases', () => {
      it('should handle target category not found in config', async () => {
        // Mock the page.goto to return quickly for all categories when non-existent category is used
        page.goto.mockImplementation(async (url) => {
          // For non-existent category test, make all category pages return quickly with no content
          page.url.mockReturnValue(url);
          return { status: () => 200 };
        });

        // Mock page.locator to return empty containers for all categories
        page.locator.mockImplementation(selector => {
          // Return empty container for any category container selector
          if (selector.includes('section.flex')) {
            return { count: vi.fn().mockResolvedValue(0) }; // No container found
          }
          return { count: vi.fn().mockResolvedValue(0) };
        });

        const articles = await scraperService.scrapeCategories(new Set(), 'non-existent-category', 1);

        // Should fall back to scraping all categories and warn, but return empty array due to no containers
        expect(articles).toBeInstanceOf(Array);
        expect(articles).toHaveLength(0); // Should be empty since no containers are found
      }, 15000); // Increase timeout to 15 seconds

      it('should handle maxArticlesToProcess limit correctly', async () => {
        // Mock multiple articles but limit processing
        page.locator.mockImplementation(selector => {
          const kozeletContainerSelector = config.mti.categoryContainerSelectors.kozelet;
          
          if (selector === kozeletContainerSelector) {
            const mockContainer = {
              count: vi.fn().mockResolvedValue(1),
              locator: vi.fn().mockImplementation(innerSelector => {
                if (innerSelector === 'a.group.mb-5.block') {
                  // Return 3 mock articles
                  const mockArticles = Array.from({ length: 3 }, (_, i) => ({
                    getAttribute: vi.fn().mockResolvedValue(`/cikkek/${i}/test-article-${i}`),
                    locator: vi.fn().mockImplementation(detailSelector => {
                      const detailLocator = {
                        count: vi.fn().mockResolvedValue(1),
                        getAttribute: vi.fn().mockResolvedValue(
                          detailSelector === 'img' ? `https://mti.hu/image-${i}.jpg` : null
                        ),
                        textContent: vi.fn().mockResolvedValue(
                          detailSelector === 'h2' ? `Test Article ${i}` :
                          detailSelector === 'h2 + div div' ? `Summary ${i}` : null
                        )
                      };
                      return detailLocator;
                    })
                  }));
                  
                  return { all: vi.fn().mockResolvedValue(mockArticles) };
                }
                return { count: vi.fn().mockResolvedValue(0) };
              })
            };
            return mockContainer;
          }
          return { count: vi.fn().mockResolvedValue(0) };
        });
        
        const articles = await scraperService.scrapeCategories(new Set(), 'kozelet', 2);
        
        // Should limit to maxArticlesToProcess
        expect(articles.length).toBeLessThanOrEqual(2);
      });

      it('should handle empty article content gracefully', async () => {
        page.locator.mockImplementation(selector => {
          if (selector === '.svelte-1tcypa1') {
            const contentLocator = {
              count: vi.fn().mockResolvedValue(1),
              locator: vi.fn().mockReturnValue({
                first: vi.fn().mockReturnValue({
                  waitFor: vi.fn().mockResolvedValue(undefined)
                })
              }),
              evaluate: vi.fn().mockResolvedValue('') // Empty content
            };
            return contentLocator;
          }
          return { count: vi.fn().mockResolvedValue(0) };
        });
        
        const result = await scraperService.scrapeArticleContent(page, {
          url: 'https://mti.hu/test-url',
          summary: 'Test summary'
        });
        
        expect(result).toEqual({
          content_html: null,
          date_published: null
        });
      });

      it('should handle scraping errors in category processing', async () => {
        // Mock scrapeCategoryPage to throw an error
        vi.spyOn(scraperService, 'scrapeCategoryPage').mockRejectedValue(new Error('Category scraping failed'));
        
        await expect(scraperService.scrapeCategories(new Set(), 'kozelet', 1))
          .rejects.toThrow('Category scraping failed');
      });

      it('should skip already existing URLs in existingArticleUrls set', async () => {
        const existingUrls = new Set([`${config.mti.baseUrl}/cikkek/123/teszt-cikk-a-kozeletbol`]);
        
        const articles = await scraperService.scrapeCategories(existingUrls, 'kozelet', 1);
        
        expect(articles).toHaveLength(0); // Should skip the existing URL
      });

      it('should handle session validation errors during article scraping', async () => {
        // Mock ensureAuthenticated to return false (authentication failure)
        vi.spyOn(scraperService, 'ensureAuthenticated').mockResolvedValue(false);
        
        const result = await scraperService.scrapeArticleContent(page, {
          url: 'https://mti.hu/test-url',
          summary: 'Test summary'
        });
        
        expect(result).toEqual({ content_html: null, date_published: null });
      });
    });

    describe('Session Validation', () => {
      it('should return false when validateSession detects login page by URL', async () => {
        page.url.mockReturnValue('https://mti.hu/bejelentkezes');
        page.title.mockResolvedValue('Test Page');
        
        const isValid = await scraperService.validateSession(page);
        
        expect(isValid).toBe(false);
      });

      it('should return false when validateSession detects login page by title', async () => {
        page.url.mockReturnValue('https://mti.hu/some-page');
        page.title.mockResolvedValue('Bejelentkezés - MTI');
        
        const isValid = await scraperService.validateSession(page);
        
        expect(isValid).toBe(false);
      });

      it('should return true for valid session', async () => {
        page.url.mockReturnValue('https://mti.hu/kozelet');
        page.title.mockResolvedValue('Közélet - MTI');
        
        const isValid = await scraperService.validateSession(page);
        
        expect(isValid).toBe(true);
      });

      it('should handle errors during session validation', async () => {
        page.url.mockImplementation(() => { throw new Error('Page error'); });
        page.title.mockRejectedValue(new Error('Title error'));
        
        const isValid = await scraperService.validateSession(page);
        
        expect(isValid).toBe(false);
      });
    });

    describe('Authentication Ensuring', () => {
      it('should handle page not available error', async () => {
        authService.getPage = vi.fn().mockImplementation(() => {
          throw new Error('Page not available');
        });
        
        const result = await scraperService.ensureAuthenticated();
        
        expect(result).toBe(false);
      });

      it('should re-initialize browser when page not available', async () => {
        authService.getPage = vi.fn().mockReturnValueOnce(null).mockReturnValue(page);
        authService.initBrowser = vi.fn().mockResolvedValue(undefined);
        vi.spyOn(scraperService, 'validateSession').mockResolvedValue(true);
        
        const result = await scraperService.ensureAuthenticated();
        
        expect(authService.initBrowser).toHaveBeenCalled();
        expect(result).toBe(true);
      });

      it('should re-authenticate when session is invalid', async () => {
        vi.spyOn(scraperService, 'validateSession').mockResolvedValue(false);
        authService.maintainSession = vi.fn().mockResolvedValue(undefined);
        authService.isAuthenticated = true;
        
        const result = await scraperService.ensureAuthenticated();
        
        expect(authService.maintainSession).toHaveBeenCalled();
        expect(result).toBe(true);
      });

      it('should return false when re-authentication fails', async () => {
        vi.spyOn(scraperService, 'validateSession').mockResolvedValue(false);
        authService.maintainSession = vi.fn().mockResolvedValue(undefined);
        authService.isAuthenticated = false; // Re-auth failed
        
        const result = await scraperService.ensureAuthenticated();
        
        expect(result).toBe(false);
      });
    });
  });
});
