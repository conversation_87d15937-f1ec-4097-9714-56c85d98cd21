/**
 * CacheManager - Manages category-specific in-memory caches
 * 
 * Features:
 * - Separate cache per category (kozelet, gazdasag, etc.)
 * - Per-category size limits to prevent memory issues
 * - Independent querying and management
 * - Aggregated view across all categories
 */
export class CacheManager {
  constructor(config, logger) {
    this.config = config;
    this.logger = logger;
    
    // Get available categories from config
    this.categories = config.mti.categories.map(cat => 
      cat === 'en/english' ? 'english' : cat
    );
    
    // Initialize category-specific caches
    this.caches = new Map();
    this.initializeCaches();
    
    this.logger.info('CacheManager initialized', {
      categories: this.categories,
      categoryLimits: this.getCategoryLimits()
    });
  }

  /**
   * Initialize empty cache structure for each category
   */
  initializeCaches() {
    const feedMetadata = {
      version: this.config.feed.version,
      home_page_url: this.config.feed.homePageUrl,
      icon: this.config.feed.icon
    };

    this.categories.forEach(category => {
      this.caches.set(category, {
        ...feedMetadata,
        title: `${this.config.feed.title} - ${this.capitalizeCategory(category)}`,
        feed_url: `http://localhost:${this.config.server.port}/feed/${category}`,
        category: category,
        items: [],
        lastUpdated: null,
        itemCount: 0
      });
    });
  }

  /**
   * Get cache size limits per category
   */
  getCategoryLimits() {
    const defaultLimit = Math.floor((this.config.scraping.cacheMaxSize || 1500) / this.categories.length);
    
    return {
      default: defaultLimit,
      // Allow specific category limits if configured
      ...this.config.scraping.categoryLimits
    };
  }

  /**
   * Get cache for a specific category
   */
  getCategory(category) {
    if (!this.caches.has(category)) {
      throw new Error(`Category '${category}' not found. Available: ${this.categories.join(', ')}`);
    }
    return this.caches.get(category);
  }

  /**
   * Update a specific category cache with new articles
   */
  updateCategory(category, newArticles) {
    if (!this.caches.has(category)) {
      this.logger.warn(`Attempted to update unknown category: ${category}`);
      return;
    }

    const cache = this.caches.get(category);
    const limits = this.getCategoryLimits();
    const categoryLimit = limits[category] || limits.default;

    // Merge new articles with existing ones
    const allItemsMap = new Map(cache.items.map(item => [item.url, item]));
    let addedCount = 0;
    
    newArticles.forEach(article => {
      if (!allItemsMap.has(article.url)) {
        addedCount++;
      }
      allItemsMap.set(article.url, article);
    });

    let allItems = Array.from(allItemsMap.values());

    // Sort by date (newest first)
    allItems.sort((a, b) => {
      const dateA = a.date_published ? new Date(a.date_published) : null;
      const dateB = b.date_published ? new Date(b.date_published) : null;
      if (dateA === null && dateB === null) return 0;
      if (dateA === null) return 1;
      if (dateB === null) return -1;
      return dateB - dateA;
    });

    // Apply category size limit
    if (allItems.length > categoryLimit) {
      const removedCount = allItems.length - categoryLimit;
      allItems = allItems.slice(0, categoryLimit);
      this.logger.info(`Category ${category} size limit applied`, {
        limit: categoryLimit,
        kept: allItems.length,
        removed: removedCount
      });
    }

    // Update cache
    cache.items = allItems;
    cache.itemCount = allItems.length;
    cache.lastUpdated = new Date().toISOString();

    this.logger.info(`Updated category cache: ${category}`, {
      newArticles: addedCount,
      totalArticles: cache.itemCount,
      limit: categoryLimit
    });
  }

  /**
   * Get aggregated feed from all categories
   */
  getAggregatedFeed() {
    const allItems = [];
    let lastUpdated = null;

    // Collect items from all categories
    this.caches.forEach((cache, category) => {
      allItems.push(...cache.items);
      
      // Track most recent update time
      if (cache.lastUpdated) {
        const cacheTime = new Date(cache.lastUpdated);
        if (!lastUpdated || cacheTime > lastUpdated) {
          lastUpdated = cacheTime;
        }
      }
    });

    // Sort all items by date
    allItems.sort((a, b) => {
      const dateA = a.date_published ? new Date(a.date_published) : null;
      const dateB = b.date_published ? new Date(b.date_published) : null;
      if (dateA === null && dateB === null) return 0;
      if (dateA === null) return 1;
      if (dateB === null) return -1;
      return dateB - dateA;
    });

    return {
      version: this.config.feed.version,
      title: this.config.feed.title,
      home_page_url: this.config.feed.homePageUrl,
      feed_url: `http://localhost:${this.config.server.port}/feed`,
      icon: this.config.feed.icon,
      items: allItems,
      lastUpdated: lastUpdated ? lastUpdated.toISOString() : null
    };
  }

  /**
   * Get statistics for all categories
   */
  getStats() {
    const stats = {
      totalArticles: 0,
      categories: {},
      limits: this.getCategoryLimits()
    };

    this.caches.forEach((cache, category) => {
      stats.totalArticles += cache.itemCount;
      stats.categories[category] = {
        count: cache.itemCount,
        lastUpdated: cache.lastUpdated,
        limit: stats.limits[category] || stats.limits.default,
        utilizationPercent: Math.round((cache.itemCount / (stats.limits[category] || stats.limits.default)) * 100)
      };
    });

    return stats;
  }

  /**
   * Clear all caches
   */
  clearAll() {
    this.caches.forEach((cache, category) => {
      cache.items = [];
      cache.itemCount = 0;
      cache.lastUpdated = null;
    });
    this.logger.info('All category caches cleared');
  }

  /**
   * Clear specific category cache
   */
  clearCategory(category) {
    if (!this.caches.has(category)) {
      throw new Error(`Category '${category}' not found`);
    }
    
    const cache = this.caches.get(category);
    cache.items = [];
    cache.itemCount = 0;
    cache.lastUpdated = null;
    
    this.logger.info(`Category cache cleared: ${category}`);
  }

  /**
   * Get all available categories
   */
  getAvailableCategories() {
    return [...this.categories];
  }

  /**
   * Helper to capitalize category names for display
   */
  capitalizeCategory(category) {
    return category.charAt(0).toUpperCase() + category.slice(1);
  }

  /**
   * Get existing URLs across all categories for scraper deduplication
   */
  getAllExistingUrls() {
    const urls = new Set();
    this.caches.forEach(cache => {
      cache.items.forEach(item => urls.add(item.url));
    });
    return urls;
  }

  /**
   * Get memory usage estimation
   */
  getMemoryUsage() {
    let totalSize = 0;
    const categoryUsage = {};

    this.caches.forEach((cache, category) => {
      const cacheSize = JSON.stringify(cache.items).length;
      totalSize += cacheSize;
      categoryUsage[category] = {
        bytes: cacheSize,
        articles: cache.itemCount,
        avgBytesPerArticle: cache.itemCount > 0 ? Math.round(cacheSize / cache.itemCount) : 0
      };
    });

    return {
      totalBytes: totalSize,
      totalMB: Math.round(totalSize / (1024 * 1024) * 100) / 100,
      categories: categoryUsage
    };
  }
}

export default CacheManager;
