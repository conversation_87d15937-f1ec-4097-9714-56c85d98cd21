import { createHash } from 'crypto';
import { processEmbeddedContent } from '../utils/embeddedContent.js';
import { AuthenticationError, NetworkError, ScrapingError } from '../utils/errors.js';
import { parseEnglishDateString, parseHungarianDateString, randomDelay } from '../utils/helpers.js';

class ScraperService {
  constructor(authService, config, logger) {
    this.authService = authService;
    this.config = config;
    this.logger = logger;
  }

  /**
   * Simulates human-like behavior, currently by enforcing random delays.
   */
  async maintainHumanBehavior() {
    const { min, max } = this.config.scraping.humanDelay;
    await randomDelay(min, max);
  }

  /**
   * Scrapes a single category page for article links, titles, summaries, and image URLs.
   * @param {import('playwright').Page} page - The Playwright page object.
   * @param {string} categoryPath - The path of the category (e.g., "kozelet").
   * @param {number|null} [maxArticlesOnPage=null] - Maximum number of articles to extract from this page.
   * @returns {Promise<Array<Object>>} A promise that resolves to an array of article data objects.
   */
  async scrapeCategoryPage(page, categoryPath, maxArticlesOnPage = null) {
    const categoryUrl = `${this.config.mti.baseUrl}/${categoryPath}`;
    this.logger.debug('Visiting category page', { categoryUrl, categoryPath });
    const articlesOnPage = [];

    try {
      // Validate session before navigating
      if (!await this.ensureAuthenticated()) {
        throw new AuthenticationError('Authentication validation failed before scraping category page', categoryPath);
      }
      this.logger.debug('Authentication validated');

      const response = await page.goto(categoryUrl, { timeout: this.config.scraping.timeout });
      this.logger.debug('Successfully navigated to category', { 
        categoryPath, 
        responseStatus: response?.status()
      });

      // Validate session after navigation (in case we got redirected to login)
      if (!await this.validateSession(page)) {
        this.logger.warn('Detected session expiration after navigation, attempting re-authentication', { categoryUrl });
        if (!await this.ensureAuthenticated()) {
          throw new AuthenticationError('Re-authentication failed after detecting session expiration', categoryPath);
        }
        // Retry navigation after re-authentication
        await page.goto(categoryUrl, { timeout: this.config.scraping.timeout });
      }

      const selectors = this.config.mti.categoryContainerSelectors;
      const containerSelector = selectors[categoryPath] || selectors.default;
      this.logger.debug('Using container selector', { categoryPath, containerSelector });
      
      const containerElement = page.locator(containerSelector);
      if (await containerElement.count() === 0) {
        // Before logging debug info, check if this is due to session expiration
        if (!await this.validateSession(page)) {
          throw new AuthenticationError('Session expired during category scraping - redirected to login page', categoryPath);
        }
        
        const pageContent = await page.content();
        const debugInfo = await this.createDebugInfo({
          url: categoryUrl,
          categoryPath,
          selector: containerSelector,
          page,
          additional: {
            selectorsConfig: selectors,
            pageStatus: response ? response.status() : 'N/A',
            pageContent
          }
        });
        this.logger.warn(`Article container not found with selector "${containerSelector}" on ${categoryUrl}. Possible reasons: selector mismatch, page structure changed, or page failed to load.${debugInfo} Check 'categoryContainerSelectors' config and inspect the page structure for ${categoryPath}.`);
        return []; // No container, no articles
      }

      let articleItems = await containerElement.locator('a.group.mb-5.block').all();
      this.logger.debug(`Found ${articleItems.length} article items on ${categoryUrl} within container ${containerSelector}`);

      if (maxArticlesOnPage !== null && articleItems.length > maxArticlesOnPage) {
        this.logger.info(`On ${categoryUrl}, limiting article preview extraction from ${articleItems.length} to ${maxArticlesOnPage} (per-category limit).`);
        articleItems = articleItems.slice(0, maxArticlesOnPage);
      }

      for (const item of articleItems) {
        try {
          const relativeArticleUrl = await item.getAttribute('href');
          const articleUrl = relativeArticleUrl ? `${this.config.mti.baseUrl}${relativeArticleUrl}` : null;
          
          const imageElement = item.locator('img');
          const imageUrl = await imageElement.count() ? await imageElement.getAttribute('src') : null;
          
          const titleElement = item.locator('h2');
          const title = await titleElement.count() ? await titleElement.textContent() : null;
          
          const summaryElement = item.locator('h2 + div div');
          const summary = await summaryElement.count() ? await summaryElement.textContent() : null;

          if (articleUrl && title) {
            // Transform categoryPath for tags - use 'english' instead of 'en/english'
            const tagName = categoryPath === 'en/english' ? 'english' : categoryPath;
            
            articlesOnPage.push({
              id: createHash('md5').update(relativeArticleUrl).digest('hex'),
              url: articleUrl,
              image: imageUrl ? imageUrl.trim() : null,
              title: title ? title.trim() : null,
              summary: summary ? summary.trim() : null,
              tags: [tagName],
            });
          } else {
            const debugInfo = await this.createDebugInfo({
              url: categoryUrl,
              categoryPath,
              additional: {
                relativeArticleUrl,
                articleUrl,
                title,
                summary: summary ? summary.trim() : null,
                imageUrl,
                itemHtml: await item.innerHTML().catch(() => 'N/A')
              }
            });
            this.logger.warn(`Skipping an item on ${categoryUrl} due to missing URL or title.${debugInfo} Check if the article item structure has changed or if href/title selectors need updating.`);
          }
        } catch (e) {
          const debugInfo = await this.createDebugInfo({
            error: e,
            url: categoryUrl,
            categoryPath,
            selector: containerSelector,
            additional: {
              itemIndex: articleItems.indexOf(item),
              totalItems: articleItems.length,
              itemHtml: await item.innerHTML().catch(() => 'N/A')
            }
          });
          this.logger.error(`Error extracting details for an article on ${categoryUrl}.${debugInfo} Check if the article item structure has changed or if selectors need updating.`);
        }
      }
      this.logger.debug(`Extracted ${articlesOnPage.length} articles from ${categoryUrl}`);
      return articlesOnPage;

    } catch (error) {
      const debugInfo = await this.createDebugInfo({
        error,
        url: categoryUrl,
        categoryPath,
        selector: this.config.mti.categoryContainerSelectors[categoryPath] || this.config.mti.categoryContainerSelectors.default,
        page
      });
      this.logger.error(`Error scraping category ${categoryUrl}.${debugInfo} Check network connectivity, authentication status, or if the website structure has changed.`);
      if (error.name === 'TimeoutError') {
        throw new NetworkError(`Timeout visiting category page: ${categoryUrl}`, null, categoryUrl);
      }
      throw new ScrapingError(`Failed to scrape category page: ${categoryUrl}`, categoryUrl);
    }
  }

  /**
   * Scrapes and cleans the content of a single article page, and extracts the publication date.
   * @param {import('playwright').Page} page - The Playwright page object.
   * @param {Object} articleData - Object containing articleUrl and summary.
   * @returns {Promise<Object|null>} An object with cleanedContentHtml and datePublished, or null.
   */
  async scrapeArticleContent(page, articleData) {
    const { url, summary } = articleData; // Changed articleUrl to url
    this.logger.debug(`Visiting article page: ${url}`); // Changed articleUrl to url
    let datePublished = null;
    let cleanedContentHtml = null;

    try {
      // Validate session before navigating to article
      if (!await this.ensureAuthenticated()) {
        throw new AuthenticationError('Authentication validation failed before scraping article content', url);
      }

      await page.goto(url, { timeout: this.config.scraping.timeout }); // Changed articleUrl to url
      this.logger.debug(`Successfully navigated to article: ${url}`); // Changed articleUrl to url

      // Validate session after navigation (in case we got redirected to login)
      if (!await this.validateSession(page)) {
        this.logger.warn(`Detected session expiration after navigation to article ${url}. Attempting re-authentication...`);
        if (!await this.ensureAuthenticated()) {
          throw new AuthenticationError('Re-authentication failed after detecting session expiration on article page', url);
        }
        // Retry navigation after re-authentication
        await page.goto(url, { timeout: this.config.scraping.timeout });
      }

      const dateElement = page.locator('span.py-0\\.75:nth-child(2)');
      if (await dateElement.count() > 0) {
        const rawDateString = await dateElement.textContent();
        if (rawDateString) {
          const trimmedDateString = rawDateString.trim();
          datePublished = parseHungarianDateString(trimmedDateString);
          if (!datePublished) {
            // Try parsing as English date if Hungarian parsing failed
            datePublished = parseEnglishDateString(trimmedDateString);
          }

          if (datePublished) {
            this.logger.debug(`Extracted and parsed date for ${url}: ${datePublished}`); // Changed articleUrl to url
          } else {
            const debugInfo = await this.createDebugInfo({
              url,
              page,
              additional: {
                rawDateString,
                trimmedDateString,
                dateElementSelector: 'span.py-0\\.75:nth-child(2)'
              }
            });
            this.logger.warn(`Could not parse date string "${trimmedDateString}" for ${url} using known formats.${debugInfo} The date format may have changed or be in an unexpected language/format.`);
          }
        }
      } else {
        const debugInfo = await this.createDebugInfo({
          url,
          page,
          additional: {
            availableDateElements: await page.locator('span').count().catch(() => 'N/A'),
            allSpanTexts: await page.locator('span').allTextContents().catch(() => [])
          }
        });
        this.logger.warn(`Date element span.py-0.75:nth-child(2) not found on ${url}.${debugInfo} Check if the date selector has changed or if the page structure is different.`);
      }

      const contentElementHandle = page.locator('.svelte-1tcypa1');
      if (await contentElementHandle.count() === 0) {
        const debugInfo = await this.createDebugInfo({
          url,
          page,
          additional: {
            availableElements: await page.locator('div[class*="svelte"]').count().catch(() => 'N/A'),
            allSvelteClasses: await page.locator('div[class*="svelte"]').getAttribute('class').catch(() => []),
            bodyClasses: await page.locator('body').getAttribute('class').catch(() => 'N/A')
          }
        });
        this.logger.warn(`Content container .svelte-1tcypa1 not found on ${url}.${debugInfo} Check if the content container selector has changed or if the page structure is different.`);
        return { content_html: null, date_published: datePublished };
      }

      // Wait for content paragraphs to become visible
      try {
        this.logger.debug(`Waiting for content paragraphs to be visible in .svelte-1tcypa1 for ${url}...`);
        await contentElementHandle.locator('p').first().waitFor({
          state: 'visible',
          // Use configured timeout, or default to 10 seconds
          timeout: this.config.scraping.contentReadyTimeout ?? 10000 
        });
        this.logger.debug(`Content paragraphs are visible for ${url}`);
      } catch (e) {
        const debugInfo = await this.createDebugInfo({
          error: e,
          url,
          page,
          additional: {
            timeout: this.config.scraping.contentReadyTimeout ?? 10000,
            contentContainerExists: await contentElementHandle.count() > 0,
            paragraphCount: await contentElementHandle.locator('p').count().catch(() => 'N/A')
          }
        });
        this.logger.warn(`Timeout or error waiting for content paragraphs in .svelte-1tcypa1 on ${url}.${debugInfo} Content might be loading slowly or the page structure has changed. Proceeding with extraction.`);
        // Content extraction will proceed, potentially with empty/partial content if timeout occurred.
      }

      const rawContentHtml = await contentElementHandle.innerHTML();
      cleanedContentHtml = processEmbeddedContent(rawContentHtml, summary);

      this.logger.debug(`Successfully extracted and cleaned content with embedded content processing from ${url}`);
      return { content_html: cleanedContentHtml, date_published: datePublished };

    } catch (error) {
      const debugInfo = await this.createDebugInfo({
        error,
        url,
        page,
        additional: {
          summary,
          dateElementExists: await page.locator('span.py-0\\.75:nth-child(2)').count().catch(() => 'N/A'),
          contentElementExists: await page.locator('.svelte-1tcypa1').count().catch(() => 'N/A')
        }
      });
      this.logger.error(`Error scraping article content for ${url}.${debugInfo} Check network connectivity, authentication status, or if the article page structure has changed.`);
      if (error.name === 'TimeoutError') {
        throw new NetworkError(`Timeout visiting article page: ${url}`, null, url); // Changed articleUrl to url (twice)
      }
      return { content_html: null, date_published: null }; // Ensure consistent return structure on error
    }
  }

  /**
   * Iterates through categories, scrapes article data, fetches full content and date, and aggregates results.
   * @param {Set<string>} [existingArticleUrls=new Set()] - A set of URLs for articles already in the cache.
   * @param {string|null} [targetCategoryPath=null] - Specific category to scrape, or null for all.
   * @param {number|null} [maxArticlesToProcess=null] - Max total NEW articles to process full content for.
   * @returns {Promise<Array<Object>>} A promise that resolves to an array of newly extracted and enriched article data.
   */
  async scrapeCategories(existingArticleUrls = new Set(), targetCategoryPath = null, maxArticlesToProcess = null) {
    this.logger.debug('Starting category scraping process...', {
      targetCategoryPath,
      maxArticlesToProcess,
      existingArticleUrlsCount: existingArticleUrls.size
    });

    // maintainSession is now called from app.js cron job logic before this
    if (!this.authService.isAuthenticated) {
      const debugInfo = await this.createDebugInfo({
        categoryPath: targetCategoryPath,
        additional: {
          authServiceType: this.authService.constructor.name,
          maxArticlesToProcess,
          existingArticleUrlsCount: existingArticleUrls.size
        }
      });
      this.logger.error(`Authentication failed (checked before scraping). Cannot proceed.${debugInfo} Check authentication service status and login credentials.`);
      return [];
    }

    const page = this.authService.getPage();
    if (!page) {
      const debugInfo = await this.createDebugInfo({
        categoryPath: targetCategoryPath,
        additional: {
          authServiceType: this.authService.constructor.name,
          pageInstance: page,
          hasGetPageMethod: typeof this.authService.getPage === 'function'
        }
      });
      this.logger.error(`Failed to get Playwright page instance from authService.${debugInfo} Check if authService is properly initialized and page instance is available.`);
      return [];
    }

    let categoriesToScrape = this.config.mti.categories;
    if (targetCategoryPath) {
      if (categoriesToScrape.includes(targetCategoryPath)) {
        categoriesToScrape = [targetCategoryPath];
      } else {
        const debugInfo = await this.createDebugInfo({
          categoryPath: targetCategoryPath,
          additional: {
            availableCategories: this.config.mti.categories,
            categoriesCount: this.config.mti.categories.length
          }
        });
        this.logger.warn(`Target category ${targetCategoryPath} not found in config. Scraping all categories.${debugInfo} Check if the category name is spelled correctly or if it needs to be added to the config.`);
      }
    }
    this.logger.debug(`Found ${categoriesToScrape.length} categories to scrape: ${categoriesToScrape.join(', ')}`);
    
    let allArticlePreviews = [];
    for (const categoryPath of categoriesToScrape) {
      try {
        // Apply the limit per category instead of globally
        const articlesFromCategory = await this.scrapeCategoryPage(page, categoryPath, maxArticlesToProcess);
        if (articlesFromCategory && articlesFromCategory.length > 0) {
          allArticlePreviews = allArticlePreviews.concat(articlesFromCategory);
          this.logger.info(`Added ${articlesFromCategory.length} articles from ${categoryPath}. Total previews now: ${allArticlePreviews.length}`);
        } else {
          this.logger.info(`No article previews extracted from ${categoryPath}.`);
        }
      } catch (error) {
        const debugInfo = await this.createDebugInfo({
          error,
          categoryPath,
          url: `${this.config.mti.baseUrl}/${categoryPath}`,
          additional: {
            maxArticlesToProcess,
            currentPreviewsCount: allArticlePreviews.length
          }
        });
        this.logger.error(`Failed to process category ${categoryPath} for previews.${debugInfo} Check network connectivity, authentication status, or if the category page structure has changed.`);

        // Stop entire processing when category scraping fails
        this.logger.error(`Stopping entire scraping process due to category ${categoryPath} failure. Will retry on next scheduled update.`);
        throw error;
      }
      // Removed the global limit check - now each category gets processed independently
      if (categoriesToScrape.indexOf(categoryPath) < categoriesToScrape.length - 1) {
        this.logger.debug(`Applying random delay before next category page...`);
        await this.maintainHumanBehavior();
      }
    }
    this.logger.info(`Finished collecting article previews. Total previews: ${allArticlePreviews.length}`);

    const newArticlePreviews = allArticlePreviews.filter(preview => !existingArticleUrls.has(preview.url));
    this.logger.info(`From ${allArticlePreviews.length} total previews, found ${newArticlePreviews.length} new previews not in cache.`);

    // Process all new articles for content enrichment (no global limit)
    // The per-category limit was already applied during preview collection
    let articlesToEnrich = newArticlePreviews;
    this.logger.info(`Processing full content for ${articlesToEnrich.length} new articles across all categories.`);

    const enrichedArticles = [];
    for (const articlePreview of articlesToEnrich) { // Iterate over articlesToEnrich instead of allArticlePreviews
      try {
        // Skip if URL already exists (double check, though filter should handle this)
        if (existingArticleUrls.has(articlePreview.url)) {
            this.logger.info(`Skipping enrichment for already cached (or recently processed) URL: ${articlePreview.url}`);
            continue;
        }
        this.logger.info(`Fetching full content: ${articlePreview.title}`);
        const articleDetails = await this.scrapeArticleContent(page, articlePreview);
        
        enrichedArticles.push({
          ...articlePreview, // id, url, image, title, summary, tags are already here
          content_html: articleDetails ? articleDetails.content_html : null,
          date_published: articleDetails ? articleDetails.date_published : null,
        });
        
        if (!articleDetails || !articleDetails.content_html) {
           const debugInfo = await this.createDebugInfo({
             url: articlePreview.url,
             additional: {
               title: articlePreview.title,
               summary: articlePreview.summary,
               datePublished: articleDetails ? articleDetails.date_published : 'N/A',
               hasArticleDetails: !!articleDetails,
               contentHtmlExists: !!(articleDetails && articleDetails.content_html),
               contentLength: articleDetails && articleDetails.content_html ? articleDetails.content_html.length : 0
             }
           });
           this.logger.warn(`Could not fetch/clean content for ${articlePreview.url}.${debugInfo} The article page may have failed to load or the content structure has changed.`);
        }

      } catch (error) {
        const debugInfo = await this.createDebugInfo({
          error,
          url: articlePreview.url,
          additional: {
            title: articlePreview.title,
            summary: articlePreview.summary,
            tags: articlePreview.tags,
            currentArticleIndex: articlesToEnrich.indexOf(articlePreview),
            totalArticlesToEnrich: articlesToEnrich.length
          }
        });
        this.logger.error(`Error processing full content for ${articlePreview.url}.${debugInfo} Check network connectivity, authentication status, or if the article page structure has changed.`);
        enrichedArticles.push({ ...articlePreview, content_html: null, date_published: null });
      }

      // Apply delay only if there are more articles to process in the articlesToEnrich list
      if (articlesToEnrich.indexOf(articlePreview) < articlesToEnrich.length - 1) {
        this.logger.debug(`Applying random delay before fetching next new article's full content...`);
        await this.maintainHumanBehavior();
      }
    }
    
    this.logger.info(`Finished enriching articles. Total enriched articles: ${enrichedArticles.length}`);
    if (enrichedArticles.length > 0) {
      this.logger.info('Sample of enriched articles (first 2 with content preview and date):');
      enrichedArticles.slice(0, 2).forEach(article => {
        const contentPreview = article.content_html ? article.content_html.substring(0, 100) + '...' : 'N/A';
        this.logger.info(JSON.stringify({ ...article, content_html: contentPreview }, null, 2));
      });
    }

    // Sort articles by date_published in descending order (newest first)
    // Articles with null dates are pushed to the end.
    enrichedArticles.sort((a, b) => {
      if (a.date_published && b.date_published) {
        return new Date(b.date_published) - new Date(a.date_published);
      }
      if (a.date_published && !b.date_published) {
        return -1; // a comes first
      }
      if (!a.date_published && b.date_published) {
        return 1; // b comes first
      }
      return 0; // both are null, keep original order relative to each other
    });

    this.logger.info(`Sorted ${enrichedArticles.length} articles by date_published (descending).`);

    return enrichedArticles;
  }

  /**
   * Creates standardized debug info for logging
   * @param {Object} params - Debug parameters
   * @param {Error} [params.error] - Error object if applicable
   * @param {string} [params.url] - Current URL being processed
   * @param {string} [params.categoryPath] - Category path being scraped
   * @param {string} [params.selector] - CSS selector being used
   * @param {import('playwright').Page} [params.page] - Playwright page object
   * @param {Object} [params.additional] - Additional debug fields
   * @returns {Promise<string>} Formatted debug info string
   */
  async createDebugInfo(params = {}) {
    const { error, url, categoryPath, selector, page, additional = {} } = params;
    
    const debugInfo = {
      // Error information (if applicable)
      ...(error && {
        error: error.message,
        errorName: error.name,
        errorStack: error.stack
      }),
      
      // URL information
      ...(url && { url }),
      ...(categoryPath && { 
        categoryPath,
        categoryUrl: `${this.config.mti.baseUrl}/${categoryPath}`
      }),
      
      // Selector information
      ...(selector && { selector }),
      
      // Page information (if page is provided)
      ...(page && {
        pageTitle: await page.title().catch(() => 'N/A'),
        pageUrl: page.url(), // page.url() is synchronous, doesn't need .catch()
        pageSource: await page.content().catch(() => 'N/A')
      }),
      
      // Authentication status
      isAuthenticated: this.authService.isAuthenticated,
      
      // Additional custom fields
      ...additional
    };

    return `\nDebug info: ${JSON.stringify(debugInfo, null, 2)}`;
  }

  /**
   * Validates that we're still authenticated by checking if we're on a login page
   * @param {import('playwright').Page} page - The Playwright page object
   * @returns {Promise<boolean>} True if still authenticated, false if redirected to login
   */
  async validateSession(page) {
    try {
      const currentUrl = page.url();
      const pageTitle = await page.title().catch(() => '');
      
      // Check if we're on a login page
      const isLoginPage = currentUrl.includes('bejelentkezes') || 
                         pageTitle.toLowerCase().includes('bejelentkezés') ||
                         pageTitle.toLowerCase().includes('login');
      
      if (isLoginPage) {
        const debugInfo = await this.createDebugInfo({
          page,
          additional: {
            currentUrl,
            authServiceAuthenticated: this.authService.isAuthenticated
          }
        });
        this.logger.warn(`Session expired - redirected to login page.${debugInfo} Attempting re-authentication...`);
        return false;
      }
      
      return true;
    } catch (error) {
      this.logger.error(`Error validating session: ${error.message}`);
      return false;
    }
  }

  /**
   * Ensures authentication is valid, re-authenticates if needed
   * @returns {Promise<boolean>} True if authenticated, false if authentication failed
   */
  async ensureAuthenticated() {
    try {
      // Check if browser/page is available
      if (!this.authService.getPage()) {
        this.logger.warn('Page not available, re-initializing browser...');
        await this.authService.initBrowser();
      }

      const page = this.authService.getPage();
      
      // Validate current session
      if (!await this.validateSession(page)) {
        this.logger.info('Session invalid, attempting re-authentication...');
        await this.authService.maintainSession();
        
        if (!this.authService.isAuthenticated) {
          this.logger.error('Re-authentication failed');
          return false;
        }
      }
      
      return true;
    } catch (error) {
      this.logger.error(`Error ensuring authentication: ${error.message}`);
      return false;
    }
  }
}

export default ScraperService;
