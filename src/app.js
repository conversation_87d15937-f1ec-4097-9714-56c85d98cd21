// Load environment variables first
import './utils/env.js';

import cors from '@fastify/cors';
import Fastify from 'fastify';
import cron from 'node-cron';
import feedRoutes from './routes/feed.js';
import healthRoutes from './routes/health.js';
import metricsRoutes from './routes/metrics.js';
import AuthService from './services/auth.js';
import CacheManager from './services/cacheManager.js';
import ScraperService from './services/scraper.js';
import configUtil from './utils/config.js';
import { globalErrorHandler } from './utils/errorHandler.js';

// Global services - these will be instantiated per app instance in buildApp
let authServiceInstance;
let scraperServiceInstance;
let cacheManagerInstance;

export const buildApp = async (testConfig = null) => {
  const isTestEnvironment = !!testConfig;

  // Configure logger based on environment
  const loggerConfig = isTestEnvironment ? {
    level: 'silent'
  } : {
    level: process.env.LOG_LEVEL || 'info',
    transport: {
      target: 'pino-pretty',
      options: {
        colorize: true,
        translateTime: 'SYS:standard'
      }
    }
  };

  const app = Fastify({
    logger: loggerConfig
  });

  authServiceInstance = new AuthService();
  scraperServiceInstance = new ScraperService(authServiceInstance, configUtil, app.log);
  cacheManagerInstance = new CacheManager(configUtil, app.log);

  app.log.info('Initialized category-based cache manager');

  // Expose cache manager for routes
  app.decorate('cacheManager', cacheManagerInstance);
  if (isTestEnvironment) {
    app.decorate('authServiceInstance', authServiceInstance);
    app.decorate('scraperServiceInstance', scraperServiceInstance);
    app.decorate('runScraperAndUpdateCache', async () => runScraperAndUpdateCacheInternal(app, authServiceInstance, scraperServiceInstance, cacheManagerInstance));
  }

  await app.register(cors, { origin: true });
  await app.register(healthRoutes);
  await app.register(feedRoutes);
  await app.register(metricsRoutes);

  // Register global error handler
  app.setErrorHandler(globalErrorHandler);

  // Add onClose hook for cleanup tasks
  app.addHook('onClose', async (instance, done) => {
    instance.log.info('App is closing. Performing cleanup tasks...');

    if (authServiceInstance && authServiceInstance.isBrowserInitialized && authServiceInstance.isBrowserInitialized()) {
      instance.log.info('Cleaning up AuthService browser...');
      await authServiceInstance.cleanup();
    }
    instance.log.info('Cleanup tasks completed.');
    done();
  });

  if (!authServiceInstance.isBrowserInitialized()) {
    try {
      await authServiceInstance.initBrowser();
      await authServiceInstance.maintainSession();
      if (authServiceInstance.isAuthenticated) {
        app.log.info('AuthService initialized and authenticated successfully during buildApp.');
      } else if (!isTestEnvironment) {
        app.log.warn('Initial authentication failed during buildApp. Scraper might not work.');
      }
    } catch (e) {
      app.log.error({ err: e }, 'Error during authService initialization in buildApp');
      if (!isTestEnvironment) throw e;
    }
  }
  return app;
};

async function runScraperAndUpdateCacheInternal(fastifyInstance, authSvc, scraperSvc, cacheManager) {
  fastifyInstance.log.info('Cron job: Starting scheduled scrape...');

  try {
    if (!authSvc.isBrowserInitialized()) {
        fastifyInstance.log.info('Cron job: Browser not initialized. Initializing...');
        await authSvc.initBrowser();
    }
    if (!authSvc.isAuthenticated) {
      fastifyInstance.log.info('Cron job: Not authenticated, attempting to maintain session/login...');
      await authSvc.maintainSession();
      if (!authSvc.isAuthenticated) {
        fastifyInstance.log.error('Cron job: Authentication failed, cannot scrape.');
        return;
      }
    }

    const existingUrls = cacheManager.getAllExistingUrls();
    let newArticles = await scraperSvc.scrapeCategories(existingUrls, null, configUtil.scraping.maxNewArticlesPerRun);

    if (!Array.isArray(newArticles)) {
      fastifyInstance.log.warn('scrapeCategories did not return an array from scraper. Treating as empty for cache update.');
      newArticles = [];
    }

    // Group articles by category and update each category cache
    const articlesByCategory = new Map();

    newArticles.forEach(article => {
      if (article.tags && article.tags.length > 0) {
        const category = article.tags[0]; // First tag is the category
        const normalizedCategory = category === 'en/english' ? 'english' : category;

        if (!articlesByCategory.has(normalizedCategory)) {
          articlesByCategory.set(normalizedCategory, []);
        }
        articlesByCategory.get(normalizedCategory).push(article);
      }
    });

    // Update each category cache
    let totalUpdated = 0;
    articlesByCategory.forEach((articles, category) => {
      try {
        cacheManager.updateCategory(category, articles);
        totalUpdated += articles.length;
      } catch (error) {
        fastifyInstance.log.error({ err: error, category }, `Failed to update category cache: ${category}`);
      }
    });

    const stats = cacheManager.getStats();
    fastifyInstance.log.info('Cron job: Scrape and cache update completed', {
      newArticles: newArticles.length,
      categoriesUpdated: articlesByCategory.size,
      totalCachedArticles: stats.totalArticles,
      categoryStats: stats.categories
    });
  } catch (error) {
    // Use structured error logging with context
    fastifyInstance.log.error('Cron job: Error during scheduled scrape', {
      error: error.message,
      errorName: error.name,
      errorCode: error.code || 'SCRAPING_ERROR'
    });
  }
}

function setupScheduledScraping(fastifyInstance) {
  const scrapeInterval = configUtil.scraping.interval || '0 * * * *';
  const timezone = configUtil.scraping.timezone || configUtil.server.timezone || "UTC";
  
  fastifyInstance.log.info(`Setting up scheduling with interval: ${scrapeInterval}, timezone: ${timezone}`);

  const maxJitterSeconds = configUtil.scraping.jitterSeconds?.max || 0; 

  cron.schedule(scrapeInterval, () => {
    const jitterSec = Math.floor(Math.random() * (maxJitterSeconds + 1));
    const jitterMs = jitterSec * 1000; 

    if (jitterMs > 0) {
      fastifyInstance.log.info(`Cron job triggered for scrape. Applying random jitter of ${jitterSec} seconds.`);
    } else {
      fastifyInstance.log.info(`Cron job triggered for scrape. No jitter applied.`);
    }

    setTimeout(() => {
      runScraperAndUpdateCacheInternal(fastifyInstance, authServiceInstance, scraperServiceInstance, fastifyInstance.cacheManager);
    }, jitterMs);
  }, {
    scheduled: true,
    timezone: timezone 
  });
}

function scheduleScrapingWithJitter(fastifyInstance, scheduleType) {
  const maxJitterSeconds = configUtil.scraping.jitterSeconds?.max || 0;
  const jitterSec = Math.floor(Math.random() * (maxJitterSeconds + 1));
  const jitterMs = jitterSec * 1000;

  if (jitterMs > 0) {
    fastifyInstance.log.info(`Cron job triggered for scrape (${scheduleType}). Applying random jitter of ${jitterSec} seconds.`);
  } else {
    fastifyInstance.log.info(`Cron job triggered for scrape (${scheduleType}). No jitter applied.`);
  }

  setTimeout(() => {
    runScraperAndUpdateCacheInternal(fastifyInstance, authServiceInstance, scraperServiceInstance, fastifyInstance.cacheManager);
  }, jitterMs);
}

const start = async () => {
  let fastifyInstance;
  try {
    fastifyInstance = await buildApp(); 

    const port = process.env.PORT || configUtil.server.port; 
    const host = process.env.HOST || configUtil.server.host; 
    
    if (fastifyInstance.cachedFeedData) {
        fastifyInstance.cachedFeedData.feed_url = `http://${host}:${port}/feed`;
    }
    
    // onClose hook is now added in buildApp function
    
    await fastifyInstance.listen({ port, host });
    fastifyInstance.log.info(`MTI Feed API running: http://${host}:${port}`);

    setupScheduledScraping(fastifyInstance);

    // Always perform initial scrape on startup
    fastifyInstance.log.info('Performing initial scrape on startup (after 5s delay)...');
    setTimeout(() => {
        fastifyInstance.log.info('Initial scrape timeout triggered, starting scrape...');
        runScraperAndUpdateCacheInternal(fastifyInstance, authServiceInstance, scraperServiceInstance, fastifyInstance.cacheManager);
    }, 5000);

    return fastifyInstance; // Return the Fastify instance for signal handler setup

  } catch (err) {
    // Use structured error logging for startup errors
    if (fastifyInstance) {
        fastifyInstance.log.error({ 
          err,
          context: 'server_startup',
          errorName: err.name,
          errorCode: err.code || 'STARTUP_ERROR'
        }, `Error during server startup: ${err.message}`);
    } else {
        console.error('Error during server startup (Fastify instance not available):', {
          error: err.message,
          errorName: err.name,
          errorCode: err.code || 'STARTUP_ERROR',
          stack: err.stack
        });
    }
    if (authServiceInstance && authServiceInstance.isBrowserInitialized && authServiceInstance.isBrowserInitialized()) {
        if(fastifyInstance) fastifyInstance.log.info('Attempting to cleanup authService browser due to startup error...');
        await authServiceInstance.cleanup();
    }
    process.exit(1);
  }
};

// Graceful shutdown function
const gracefulShutdown = async (signal, fastifyAppInstance) => {
  if (!fastifyAppInstance) {
    console.log(`Received ${signal}. No Fastify instance to close. Shutting down authService if initialized.`);
    if (authServiceInstance && authServiceInstance.isBrowserInitialized && authServiceInstance.isBrowserInitialized()) {
      console.log('Cleaning up AuthService browser...');
      await authServiceInstance.cleanup();
    }
    console.log('Shutdown complete.');
    process.exit(0);
  }

  fastifyAppInstance.log.info(`Received ${signal}. Shutting down gracefully...`);
  try {
    await fastifyAppInstance.close(); // This will trigger the 'onClose' hook
    // authService cleanup is now in onClose hook
    fastifyAppInstance.log.info('Shutdown complete (triggered by signal).');
    process.exit(0);
  } catch (err) {
    fastifyAppInstance.log.error({ 
      err,
      context: 'graceful_shutdown',
      signal,
      errorName: err.name,
      errorCode: err.code || 'SHUTDOWN_ERROR'
    }, `Error during graceful shutdown (triggered by ${signal}): ${err.message}`);
    process.exit(1);
  }
};

if (process.env.NODE_ENV !== 'test') {
  start().then(fastifyInstance => {
    // Setup signal handlers for graceful shutdown
    if (fastifyInstance) {
      process.on('SIGINT', () => gracefulShutdown('SIGINT', fastifyInstance));
      process.on('SIGTERM', () => gracefulShutdown('SIGTERM', fastifyInstance));
    }
  }).catch(err => {
    // Error logging is handled within start()
  });
}
